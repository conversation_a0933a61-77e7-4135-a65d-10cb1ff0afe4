using UnityEngine;
using System.Collections;

/// <summary>
/// Script demo để hướng dẫn sử dụng MeshOptimizer
/// </summary>
public class MeshOptimizerDemo : MonoBehaviour
{
    [Header("Demo Settings")]
    public GameObject[] tilePrefabs;
    public int gridSize = 20;
    public float tileSpacing = 1f;
    public bool autoOptimizeAfterGeneration = true;
    
    [Header("Performance Testing")]
    public bool enablePerformanceTest = false;
    public int performanceTestIterations = 100;
    
    private MeshOptimizer meshOptimizer;
    private float generationTime;
    private float optimizationTime;
    
    private void Start()
    {
        meshOptimizer = GetComponent<MeshOptimizer>();
        if (meshOptimizer == null)
        {
            meshOptimizer = gameObject.AddComponent<MeshOptimizer>();
        }
    }
    
    /// <summary>
    /// Tạo grid demo tiles
    /// </summary>
    [ContextMenu("Tạo Demo Grid (Generate Demo Grid)")]
    public void GenerateDemoGrid()
    {
        StartCoroutine(GenerateDemoGridCoroutine());
    }
    
    private IEnumerator GenerateDemoGridCoroutine()
    {
        Debug.Log("🏗️ Bắt đầu tạo demo grid...");
        float startTime = Time.realtimeSinceStartup;
        
        // Xóa tiles cũ
        ClearExistingTiles();
        
        // Tạo tiles mới
        int tilesCreated = 0;
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                Vector3 position = new Vector3(x * tileSpacing, 0, z * tileSpacing);
                CreateTileAt(position);
                tilesCreated++;
                
                // Yield mỗi 50 tiles để tránh lag
                if (tilesCreated % 50 == 0)
                {
                    yield return null;
                }
            }
        }
        
        generationTime = Time.realtimeSinceStartup - startTime;
        Debug.Log($"✅ Tạo {tilesCreated} tiles trong {generationTime:F2}s");
        
        // Tự động tối ưu nếu được bật
        if (autoOptimizeAfterGeneration)
        {
            yield return new WaitForSeconds(0.5f);
            OptimizeDemo();
        }
    }
    
    /// <summary>
    /// Tạo tile tại vị trí cụ thể
    /// </summary>
    private void CreateTileAt(Vector3 position)
    {
        if (tilePrefabs == null || tilePrefabs.Length == 0)
        {
            // Tạo cube đơn giản nếu không có prefab
            GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.transform.SetParent(transform);
            cube.transform.position = position;
            cube.name = $"DemoTile_{position.x}_{position.z}";
            return;
        }
        
        // Chọn prefab ngẫu nhiên
        GameObject prefab = tilePrefabs[Random.Range(0, tilePrefabs.Length)];
        GameObject tile = Instantiate(prefab, position, Quaternion.identity, transform);
        tile.name = $"DemoTile_{position.x}_{position.z}";
    }
    
    /// <summary>
    /// Xóa tiles hiện có
    /// </summary>
    private void ClearExistingTiles()
    {
        // Khôi phục tối ưu hóa trước
        if (meshOptimizer != null)
        {
            meshOptimizer.RestoreToOriginal();
        }
        
        // Xóa tất cả tiles
        for (int i = transform.childCount - 1; i >= 0; i--)
        {
            Transform child = transform.GetChild(i);
            if (child.name.StartsWith("DemoTile_") || child.name.StartsWith("CombinedMesh"))
            {
                DestroyImmediate(child.gameObject);
            }
        }
    }
    
    /// <summary>
    /// Chạy tối ưu hóa demo
    /// </summary>
    [ContextMenu("Tối Ưu Demo (Optimize Demo)")]
    public void OptimizeDemo()
    {
        if (meshOptimizer == null)
        {
            Debug.LogError("Không tìm thấy MeshOptimizer!");
            return;
        }
        
        Debug.Log("🚀 Bắt đầu tối ưu hóa...");
        float startTime = Time.realtimeSinceStartup;
        
        meshOptimizer.OptimizeMeshes();
        
        optimizationTime = Time.realtimeSinceStartup - startTime;
        Debug.Log($"✅ Tối ưu hóa hoàn tất trong {optimizationTime:F2}s");
        
        // In thống kê
        meshOptimizer.PrintOptimizationStats();
    }
    
    /// <summary>
    /// Khôi phục demo
    /// </summary>
    [ContextMenu("Khôi Phục Demo (Restore Demo)")]
    public void RestoreDemo()
    {
        if (meshOptimizer != null)
        {
            meshOptimizer.RestoreToOriginal();
        }
        Debug.Log("✅ Đã khôi phục demo!");
    }
    
    /// <summary>
    /// Chạy test hiệu suất
    /// </summary>
    [ContextMenu("Test Hiệu Suất (Performance Test)")]
    public void RunPerformanceTest()
    {
        if (!enablePerformanceTest)
        {
            Debug.LogWarning("Performance test bị tắt. Bật enablePerformanceTest để chạy.");
            return;
        }
        
        StartCoroutine(PerformanceTestCoroutine());
    }
    
    private IEnumerator PerformanceTestCoroutine()
    {
        Debug.Log("🔬 Bắt đầu performance test...");
        
        float totalOptimizationTime = 0f;
        float totalRestoreTime = 0f;
        
        for (int i = 0; i < performanceTestIterations; i++)
        {
            // Test optimization
            float startTime = Time.realtimeSinceStartup;
            meshOptimizer.OptimizeMeshes();
            totalOptimizationTime += Time.realtimeSinceStartup - startTime;
            
            yield return null;
            
            // Test restore
            startTime = Time.realtimeSinceStartup;
            meshOptimizer.RestoreToOriginal();
            totalRestoreTime += Time.realtimeSinceStartup - startTime;
            
            yield return null;
            
            if (i % 10 == 0)
            {
                Debug.Log($"Progress: {i}/{performanceTestIterations}");
            }
        }
        
        float avgOptimizationTime = totalOptimizationTime / performanceTestIterations;
        float avgRestoreTime = totalRestoreTime / performanceTestIterations;
        
        Debug.Log("=== PERFORMANCE TEST RESULTS ===");
        Debug.Log($"Iterations: {performanceTestIterations}");
        Debug.Log($"Avg Optimization Time: {avgOptimizationTime * 1000:F2}ms");
        Debug.Log($"Avg Restore Time: {avgRestoreTime * 1000:F2}ms");
        Debug.Log($"Total Test Time: {totalOptimizationTime + totalRestoreTime:F2}s");
    }
    
    /// <summary>
    /// So sánh hiệu suất trước và sau tối ưu
    /// </summary>
    [ContextMenu("So Sánh Hiệu Suất (Compare Performance)")]
    public void ComparePerformance()
    {
        StartCoroutine(ComparePerformanceCoroutine());
    }
    
    private IEnumerator ComparePerformanceCoroutine()
    {
        Debug.Log("📊 Bắt đầu so sánh hiệu suất...");
        
        // Đảm bảo ở trạng thái ban đầu
        RestoreDemo();
        yield return new WaitForSeconds(0.5f);
        
        // Đo hiệu suất trước tối ưu
        int beforeDrawCalls = GetDrawCallCount();
        int beforeVertices = GetTotalVertexCount();
        
        Debug.Log($"TRƯỚC TỐI ƯU: {beforeDrawCalls} draw calls, {beforeVertices:N0} vertices");
        
        // Tối ưu hóa
        OptimizeDemo();
        yield return new WaitForSeconds(0.5f);
        
        // Đo hiệu suất sau tối ưu
        int afterDrawCalls = GetDrawCallCount();
        int afterVertices = GetTotalVertexCount();
        
        Debug.Log($"SAU TỐI ƯU: {afterDrawCalls} draw calls, {afterVertices:N0} vertices");
        
        // Tính toán cải thiện
        float drawCallImprovement = (1f - (float)afterDrawCalls / beforeDrawCalls) * 100f;
        float vertexReduction = (1f - (float)afterVertices / beforeVertices) * 100f;
        
        Debug.Log("=== KẾT QUẢ SO SÁNH ===");
        Debug.Log($"Giảm Draw Calls: {drawCallImprovement:F1}%");
        Debug.Log($"Giảm Vertices: {vertexReduction:F1}%");
    }
    
    /// <summary>
    /// Đếm số draw calls (ước tính)
    /// </summary>
    private int GetDrawCallCount()
    {
        MeshRenderer[] renderers = GetComponentsInChildren<MeshRenderer>();
        return renderers.Length;
    }
    
    /// <summary>
    /// Đếm tổng số vertices
    /// </summary>
    private int GetTotalVertexCount()
    {
        int totalVertices = 0;
        MeshFilter[] meshFilters = GetComponentsInChildren<MeshFilter>();
        
        foreach (MeshFilter mf in meshFilters)
        {
            if (mf.sharedMesh != null)
            {
                totalVertices += mf.sharedMesh.vertexCount;
            }
        }
        
        return totalVertices;
    }
    
    /// <summary>
    /// Hiển thị hướng dẫn sử dụng
    /// </summary>
    [ContextMenu("Hướng Dẫn (Show Instructions)")]
    public void ShowInstructions()
    {
        string instructions = @"
🔧 HƯỚNG DẪN SỬ DỤNG MESH OPTIMIZER DEMO

1. 🏗️ TẠO DEMO:
   • Gán tile prefabs vào tilePrefabs array
   • Điều chỉnh gridSize và tileSpacing
   • Nhấn 'Tạo Demo Grid' hoặc gọi GenerateDemoGrid()

2. 🚀 TỐI ƯU HÓA:
   • Nhấn 'Tối Ưu Demo' để chạy tối ưu hóa
   • Xem Console để theo dõi kết quả

3. 📊 KIỂM TRA:
   • Dùng 'So Sánh Hiệu Suất' để xem cải thiện
   • Dùng 'Test Hiệu Suất' để đo thời gian xử lý

4. 🔄 KHÔI PHỤC:
   • Nhấn 'Khôi Phục Demo' để về trạng thái ban đầu

💡 MẸO:
   • Bắt đầu với grid nhỏ (10x10) để test
   • Kiểm tra Frame Debugger để xem draw calls
   • Dùng Profiler để đo hiệu suất chính xác";

        Debug.Log(instructions);
    }
}
